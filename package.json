{"name": "home-mcp", "packageManager": "yarn@4.9.2", "type": "module", "version": "0.1.0", "devDependencies": {"@eslint/js": "^9.28.0", "@tsconfig/node22": "^22.0.2", "@tsconfig/strictest": "^2.0.5", "@types/node": "^22", "eslint": "^9.28.0", "prettier": "^3.5.3", "tsx": "^4.19.4", "typescript": "^5.8.3", "typescript-eslint": "^8.33.1"}, "dependencies": {"@doist/todoist-api-typescript": "^5.0.0", "@modelcontextprotocol/sdk": "^1.12.1", "commander": "^14.0.0", "zod": "^3.25.57"}}