import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import { TodoistApi } from '@doist/todoist-api-typescript';
import { Command, Option } from 'commander';
import { z } from 'zod';

type CliOptions = {
  todoistToken: string;
  cleaningPrepProject: string;
}

// Parse command line arguments
const program = new Command();
program
  .name('home-mcp')
  .description('MCP server with Todoist integration')
  .version('0.1.0')
  .addOption(new Option('--todoist-token <token>', 'Todoist API token')
    .env('TODOIST_API_TOKEN')
    .makeOptionMandatory(true))
  .addOption(new Option('--cleaning-prep-project <project>', 'Todoist project ID for cleaning prep routine')
    .env('TODOIST_CLEANING_PREP_PROJECT')
    .makeOptionMandatory(true))
  .parse();

const { todoistToken, cleaningPrepProject } = program.opts<CliOptions>();

// Get API token from CLI argument or environment variable

if (!todoistToken || typeof todoistToken !== 'string') {
  console.error('Error: Todoist API token is required. Provide it via --token argument or TODOIST_API_TOKEN environment variable.');
  process.exit(1);
}

// Initialize Todoist API client
const todoist = new TodoistApi(todoistToken);

// Create an MCP server
const server = new McpServer({
  name: 'home-mcp',
  version: '0.1.0',
});

// Add a simple ping tool that responds with "pong"
server.tool(
  'ping',
  {}, // No parameters needed for ping
  () => ({
    content: [{ type: 'text', text: 'pong' }],
  })
);

// Add Todoist tools
server.tool(
  'list_projects',
  {}, // No parameters needed
  async () => {
    try {
      const projects = await todoist.getProjects();
      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify(projects, null, 2),
          },
        ],
      };
    } catch (error) {
      return {
        content: [
          {
            type: 'text',
            text: `Error fetching projects: ${error instanceof Error ? error.message : String(error)}`,
          },
        ],
        isError: true,
      };
    }
  }
);

server.tool(
  'list_tasks',
  {
    projectId: z.string().describe('The ID of the project to list tasks from'),
  },
  async ({ projectId }) => {
    try {
      const tasks = await todoist.getTasks({ projectId });
      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify(tasks, null, 2),
          },
        ],
      };
    } catch (error) {
      return {
        content: [
          {
            type: 'text',
            text: `Error fetching tasks for project ${projectId}: ${error instanceof Error ? error.message : String(error)}`,
          },
        ],
        isError: true,
      };
    }
  }
);

server.tool(
  'create_task',
  {
    content: z.string().describe('The content/title of the task'),
    projectId: z.string().describe('The ID of the project to create the task in'),
    description: z.string().optional().describe('Optional description for the task'),
    dueString: z.string().optional().describe('Optional due date in natural language (e.g., "tomorrow", "next Monday")'),
    priority: z.number().min(1).max(4).optional().describe('Task priority (1-4, where 4 is highest priority)'),
    sectionId: z.string().optional().describe('Optional ID of the section to create the task in'),
  },
  async ({ content, projectId, description, dueString, priority, sectionId }) => {
    try {
      const task = await todoist.addTask({
        content,
        projectId,
        description,
        dueString,
        priority,
        sectionId,
      });
      return {
        content: [
          {
            type: 'text',
            text: `Task created successfully:\n${JSON.stringify(task, null, 2)}`,
          },
        ],
      };
    } catch (error) {
      return {
        content: [
          {
            type: 'text',
            text: `Error creating task: ${error instanceof Error ? error.message : String(error)}`,
          },
        ],
        isError: true,
      };
    }
  }
);

server.tool(
  'update_task',
  {
    id: z.string().describe('The ID of the task to update'),
    content: z.string().optional().describe('The new content/title of the task'),
    description: z.string().optional().describe('The new description for the task'),
    dueString: z.string().optional().describe('The new due date in natural language (e.g., "tomorrow", "next Monday")'),
    priority: z.number().min(1).max(4).optional().describe('The new task priority (1-4, where 4 is highest priority)'),
    labels: z.array(z.string()).optional().describe('Array of label names to assign to the task'),
  },
  async ({ id, content, description, dueString, priority, labels }) => {
    try {
      const task = await todoist.updateTask(id, {
        content,
        description,
        dueString,
        priority,
        labels,
      });
      return {
        content: [
          {
            type: 'text',
            text: `Task updated successfully:\n${JSON.stringify(task, null, 2)}`,
          },
        ],
      };
    } catch (error) {
      return {
        content: [
          {
            type: 'text',
            text: `Error updating task: ${error instanceof Error ? error.message : String(error)}`,
          },
        ],
        isError: true,
      };
    }
  }
);

server.tool(
  'delete_task',
  {
    id: z.string().describe('The ID of the task to delete'),
  },
  async ({ id }) => {
    try {
      await todoist.deleteTask(id);
      return {
        content: [
          {
            type: 'text',
            text: `Task with ID ${id} deleted successfully`,
          },
        ],
      };
    } catch (error) {
      return {
        content: [
          {
            type: 'text',
            text: `Error deleting task: ${error instanceof Error ? error.message : String(error)}`,
          },
        ],
        isError: true,
      };
    }
  }
);

server.tool(
  'list_labels',
  {}, // No parameters needed
  async () => {
    try {
      const labels = await todoist.getLabels({});
      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify(labels, null, 2),
          },
        ],
      };
    } catch (error) {
      return {
        content: [
          {
            type: 'text',
            text: `Error fetching labels: ${error instanceof Error ? error.message : String(error)}`,
          },
        ],
        isError: true,
      };
    }
  }
);

server.tool(
  'create_label',
  {
    name: z.string().describe('The name of the label'),
    color: z.string().optional().describe('The color of the label (e.g., "berry_red", "red", "orange", "yellow", "olive_green", "lime_green", "green", "mint_green", "teal", "sky_blue", "light_blue", "blue", "grape", "violet", "lavender", "magenta", "salmon", "charcoal", "grey", "taupe")'),
    order: z.number().optional().describe('The order of the label'),
    isFavorite: z.boolean().optional().describe('Whether the label is a favorite'),
  },
  async ({ name, color, order, isFavorite }) => {
    try {
      const labelArgs: { name: string; color?: string | number; order?: number | null; isFavorite?: boolean } = { name };
      if (color !== undefined) labelArgs.color = color;
      if (order !== undefined) labelArgs.order = order;
      if (isFavorite !== undefined) labelArgs.isFavorite = isFavorite;

      const label = await todoist.addLabel(labelArgs);
      return {
        content: [
          {
            type: 'text',
            text: `Label created successfully:\n${JSON.stringify(label, null, 2)}`,
          },
        ],
      };
    } catch (error) {
      return {
        content: [
          {
            type: 'text',
            text: `Error creating label: ${error instanceof Error ? error.message : String(error)}`,
          },
        ],
        isError: true,
      };
    }
  }
);

server.tool(
  'delete_label',
  {
    id: z.string().describe('The ID of the label to delete'),
  },
  async ({ id }) => {
    try {
      await todoist.deleteLabel(id);
      return {
        content: [
          {
            type: 'text',
            text: `Label with ID ${id} deleted successfully`,
          },
        ],
      };
    } catch (error) {
      return {
        content: [
          {
            type: 'text',
            text: `Error deleting label: ${error instanceof Error ? error.message : String(error)}`,
          },
        ],
        isError: true,
      };
    }
  }
);

server.tool(
  'get_tasks_by_filter',
  {
    query: z.string().describe('The filter query string (e.g., "today", "overdue", "@work", "p1")'),
    lang: z.string().optional().describe('Language for natural language processing (e.g., "en", "es", "fr")'),
    cursor: z.string().optional().describe('Cursor for pagination'),
    limit: z.number().min(1).max(200).optional().describe('Maximum number of tasks to return (1-200, default: 50)'),
  },
  async ({ query, lang, cursor, limit }) => {
    try {
      const filterArgs: { query: string; lang?: string; cursor?: string | null; limit?: number } = { query };
      if (lang !== undefined) filterArgs.lang = lang;
      if (cursor !== undefined) filterArgs.cursor = cursor;
      if (limit !== undefined) filterArgs.limit = limit;

      const tasks = await todoist.getTasksByFilter(filterArgs);
      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify(tasks, null, 2),
          },
        ],
      };
    } catch (error) {
      return {
        content: [
          {
            type: 'text',
            text: `Error fetching tasks by filter "${query}": ${error instanceof Error ? error.message : String(error)}`,
          },
        ],
        isError: true,
      };
    }
  }
);

server.tool(
  'complete_task',
  {
    id: z.string().describe('The ID of the task to complete'),
  },
  async ({ id }) => {
    try {
      await todoist.closeTask(id);
      return {
        content: [
          {
            type: 'text',
            text: `Task with ID ${id} completed successfully`,
          },
        ],
      };
    } catch (error) {
      return {
        content: [
          {
            type: 'text',
            text: `Error completing task: ${error instanceof Error ? error.message : String(error)}`,
          },
        ],
        isError: true,
      };
    }
  }
);

// Section management tools
server.tool(
  'list_sections',
  {
    projectId: z.string().describe('The ID of the project to list sections from'),
  },
  async ({ projectId }) => {
    try {
      const sections = await todoist.getSections({ projectId });
      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify(sections, null, 2),
          },
        ],
      };
    } catch (error) {
      return {
        content: [
          {
            type: 'text',
            text: `Error fetching sections for project ${projectId}: ${error instanceof Error ? error.message : String(error)}`,
          },
        ],
        isError: true,
      };
    }
  }
);

server.tool(
  'create_section',
  {
    name: z.string().describe('The name of the section'),
    projectId: z.string().describe('The ID of the project to create the section in'),
    order: z.number().optional().describe('Section position among other sections from the same project'),
  },
  async ({ name, projectId, order }) => {
    try {
      const sectionArgs: { name: string; projectId: string; order?: number } = { name, projectId };
      if (order !== undefined) sectionArgs.order = order;

      const section = await todoist.addSection(sectionArgs);
      return {
        content: [
          {
            type: 'text',
            text: `Section created successfully:\n${JSON.stringify(section, null, 2)}`,
          },
        ],
      };
    } catch (error) {
      return {
        content: [
          {
            type: 'text',
            text: `Error creating section: ${error instanceof Error ? error.message : String(error)}`,
          },
        ],
        isError: true,
      };
    }
  }
);

server.tool(
  'update_section',
  {
    id: z.string().describe('The ID of the section to update'),
    name: z.string().describe('The new name of the section'),
  },
  async ({ id, name }) => {
    try {
      const section = await todoist.updateSection(id, { name });
      return {
        content: [
          {
            type: 'text',
            text: `Section updated successfully:\n${JSON.stringify(section, null, 2)}`,
          },
        ],
      };
    } catch (error) {
      return {
        content: [
          {
            type: 'text',
            text: `Error updating section: ${error instanceof Error ? error.message : String(error)}`,
          },
        ],
        isError: true,
      };
    }
  }
);

server.tool(
  'delete_section',
  {
    id: z.string().describe('The ID of the section to delete'),
  },
  async ({ id }) => {
    try {
      await todoist.deleteSection(id);
      return {
        content: [
          {
            type: 'text',
            text: `Section with ID ${id} deleted successfully`,
          },
        ],
      };
    } catch (error) {
      return {
        content: [
          {
            type: 'text',
            text: `Error deleting section: ${error instanceof Error ? error.message : String(error)}`,
          },
        ],
        isError: true,
      };
    }
  }
);

server.tool(
  'move_task_to_section',
  {
    taskId: z.string().describe('The ID of the task to move'),
    sectionId: z.string().describe('The ID of the section to move the task to'),
  },
  async ({ taskId, sectionId }) => {
    try {
      const tasks = await todoist.moveTasks([taskId], { sectionId });
      return {
        content: [
          {
            type: 'text',
            text: `Task moved successfully:\n${JSON.stringify(tasks[0], null, 2)}`,
          },
        ],
      };
    } catch (error) {
      return {
        content: [
          {
            type: 'text',
            text: `Error moving task: ${error instanceof Error ? error.message : String(error)}`,
          },
        ],
        isError: true,
      };
    }
  }
);

server.prompt(
  'cleaning_prep_routine',
  'Create tasks in todoist to prepare for a cleaning day.',
  {
    cleaningDate: z.string()
      .refine(arg => {
        if (!/^\d{4}-\d{2}-\d{2}$/.test(arg)) {
          throw new Error('Invalid date format. Please use YYYY-MM-DD.');
        }
        return true;
      })
      .describe('Cleaning day to prepare for (Date in YYYY-MM-DD format)'),
  },
  async ({ cleaningDate: cleaningDateStr }) => {
    const cleaningDate = new Date(cleaningDateStr);
    const nightBefore = new Date(cleaningDate.getTime());
    nightBefore.setDate(nightBefore.getDate() - 1);

    const nightBeforeStr = nightBefore.toISOString().split('T')[0];


    const prompt = `
      Delete all sections and tasks from project ${cleaningPrepProject}
      Create a new section named "Night Before" in ${cleaningPrepProject}

      Create the following tasks in the "Night Before" section:
      - "Wash whites"
      - "Tidy up living room"
      - "Tidy up kitchen"
      - "Tidy up second floor"
      - "Put all clothes in hamper"
      - "Clear out my stuff from shower"
      All tasks in the "Night Before" section are due on ${nightBeforeStr} at 9 PM

      Create a new section named "Morning Of" in ${cleaningPrepProject}

      Create the following tasks in the "Morning Of" section:
      - "Make bed"
      - "Clear off dining room table"
      - "Clear off bathroom counter"
      - "Clear out shower"
      - "Wash shower curtain"
      - "Wash bathroom rug"
      - "Put dishes in dishwasher"

      All tasks in the "Morning Of" section are due on ${cleaningDateStr} at 9 AM
    `;

    return {
      messages: [
        {
          role: 'user',
          content: {
            type: 'text',
            text: prompt
          },
        },
      ],
    };
  }
);

// Note: Filter management tools (list_filters, create_filter, delete_filter) are not implemented
// because they are only available through the Todoist Sync API, not the REST API that the
// @doist/todoist-api-typescript client wraps. Filter operations would require implementing
// direct Sync API calls, which is beyond the scope of this REST API-based MCP server.

// Start the server with stdio transport
const transport = new StdioServerTransport();
await server.connect(transport);
