import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import { TodoistApi } from '@doist/todoist-api-typescript';
import { Command, Option } from 'commander';
import { z } from 'zod';

type CliOptions = {
  todoistToken: string;
}

// Parse command line arguments
const program = new Command();
program
  .name('home-mcp')
  .description('MCP server with Todoist integration')
  .version('0.1.0')
  .addOption(new Option('--todoist-token <token>', 'Todoist API token')
    .env('TODOIST_API_TOKEN')
    .makeOptionMandatory(true))
  .parse();

const { todoistToken } = program.opts<CliOptions>();

// Get API token from CLI argument or environment variable

if (!todoistToken || typeof todoistToken !== 'string') {
  console.error('Error: Todoist API token is required. Provide it via --token argument or TODOIST_API_TOKEN environment variable.');
  process.exit(1);
}

// Initialize Todoist API client
const todoist = new TodoistApi(todoistToken);

// Create an MCP server
const server = new McpServer({
  name: 'home-mcp',
  version: '0.1.0',
});

// Add a simple ping tool that responds with "pong"
server.tool(
  'ping',
  {}, // No parameters needed for ping
  () => ({
    content: [{ type: 'text', text: 'pong' }],
  })
);

// Add Todoist tools
server.tool(
  'list_projects',
  {}, // No parameters needed
  async () => {
    try {
      const projects = await todoist.getProjects();
      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify(projects, null, 2),
          },
        ],
      };
    } catch (error) {
      return {
        content: [
          {
            type: 'text',
            text: `Error fetching projects: ${error instanceof Error ? error.message : String(error)}`,
          },
        ],
        isError: true,
      };
    }
  }
);

server.tool(
  'list_tasks',
  {
    projectId: z.string().describe('The ID of the project to list tasks from'),
  },
  async ({ projectId }) => {
    try {
      const tasks = await todoist.getTasks({ projectId });
      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify(tasks, null, 2),
          },
        ],
      };
    } catch (error) {
      return {
        content: [
          {
            type: 'text',
            text: `Error fetching tasks for project ${projectId}: ${error instanceof Error ? error.message : String(error)}`,
          },
        ],
        isError: true,
      };
    }
  }
);

server.tool(
  'create_task',
  {
    content: z.string().describe('The content/title of the task'),
    projectId: z.string().describe('The ID of the project to create the task in'),
    description: z.string().optional().describe('Optional description for the task'),
    dueString: z.string().optional().describe('Optional due date in natural language (e.g., "tomorrow", "next Monday")'),
    priority: z.number().min(1).max(4).optional().describe('Task priority (1-4, where 4 is highest priority)'),
  },
  async ({ content, projectId, description, dueString, priority }) => {
    try {
      const task = await todoist.addTask({
        content,
        projectId,
        description,
        dueString,
        priority,
      });
      return {
        content: [
          {
            type: 'text',
            text: `Task created successfully:\n${JSON.stringify(task, null, 2)}`,
          },
        ],
      };
    } catch (error) {
      return {
        content: [
          {
            type: 'text',
            text: `Error creating task: ${error instanceof Error ? error.message : String(error)}`,
          },
        ],
        isError: true,
      };
    }
  }
);

server.tool(
  'update_task',
  {
    id: z.string().describe('The ID of the task to update'),
    content: z.string().optional().describe('The new content/title of the task'),
    description: z.string().optional().describe('The new description for the task'),
    dueString: z.string().optional().describe('The new due date in natural language (e.g., "tomorrow", "next Monday")'),
    priority: z.number().min(1).max(4).optional().describe('The new task priority (1-4, where 4 is highest priority)'),
    labels: z.array(z.string()).optional().describe('Array of label names to assign to the task'),
  },
  async ({ id, content, description, dueString, priority, labels }) => {
    try {
      const task = await todoist.updateTask(id, {
        content,
        description,
        dueString,
        priority,
        labels,
      });
      return {
        content: [
          {
            type: 'text',
            text: `Task updated successfully:\n${JSON.stringify(task, null, 2)}`,
          },
        ],
      };
    } catch (error) {
      return {
        content: [
          {
            type: 'text',
            text: `Error updating task: ${error instanceof Error ? error.message : String(error)}`,
          },
        ],
        isError: true,
      };
    }
  }
);

server.tool(
  'delete_task',
  {
    id: z.string().describe('The ID of the task to delete'),
  },
  async ({ id }) => {
    try {
      await todoist.deleteTask(id);
      return {
        content: [
          {
            type: 'text',
            text: `Task with ID ${id} deleted successfully`,
          },
        ],
      };
    } catch (error) {
      return {
        content: [
          {
            type: 'text',
            text: `Error deleting task: ${error instanceof Error ? error.message : String(error)}`,
          },
        ],
        isError: true,
      };
    }
  }
);

server.tool(
  'list_labels',
  {}, // No parameters needed
  async () => {
    try {
      const labels = await todoist.getLabels({});
      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify(labels, null, 2),
          },
        ],
      };
    } catch (error) {
      return {
        content: [
          {
            type: 'text',
            text: `Error fetching labels: ${error instanceof Error ? error.message : String(error)}`,
          },
        ],
        isError: true,
      };
    }
  }
);

server.tool(
  'create_label',
  {
    name: z.string().describe('The name of the label'),
    color: z.string().optional().describe('The color of the label (e.g., "berry_red", "red", "orange", "yellow", "olive_green", "lime_green", "green", "mint_green", "teal", "sky_blue", "light_blue", "blue", "grape", "violet", "lavender", "magenta", "salmon", "charcoal", "grey", "taupe")'),
    order: z.number().optional().describe('The order of the label'),
    isFavorite: z.boolean().optional().describe('Whether the label is a favorite'),
  },
  async ({ name, color, order, isFavorite }) => {
    try {
      const labelArgs: { name: string; color?: string | number; order?: number | null; isFavorite?: boolean } = { name };
      if (color !== undefined) labelArgs.color = color;
      if (order !== undefined) labelArgs.order = order;
      if (isFavorite !== undefined) labelArgs.isFavorite = isFavorite;

      const label = await todoist.addLabel(labelArgs);
      return {
        content: [
          {
            type: 'text',
            text: `Label created successfully:\n${JSON.stringify(label, null, 2)}`,
          },
        ],
      };
    } catch (error) {
      return {
        content: [
          {
            type: 'text',
            text: `Error creating label: ${error instanceof Error ? error.message : String(error)}`,
          },
        ],
        isError: true,
      };
    }
  }
);

server.tool(
  'delete_label',
  {
    id: z.string().describe('The ID of the label to delete'),
  },
  async ({ id }) => {
    try {
      await todoist.deleteLabel(id);
      return {
        content: [
          {
            type: 'text',
            text: `Label with ID ${id} deleted successfully`,
          },
        ],
      };
    } catch (error) {
      return {
        content: [
          {
            type: 'text',
            text: `Error deleting label: ${error instanceof Error ? error.message : String(error)}`,
          },
        ],
        isError: true,
      };
    }
  }
);

server.tool(
  'get_tasks_by_filter',
  {
    query: z.string().describe('The filter query string (e.g., "today", "overdue", "@work", "p1")'),
    lang: z.string().optional().describe('Language for natural language processing (e.g., "en", "es", "fr")'),
    cursor: z.string().optional().describe('Cursor for pagination'),
    limit: z.number().min(1).max(200).optional().describe('Maximum number of tasks to return (1-200, default: 50)'),
  },
  async ({ query, lang, cursor, limit }) => {
    try {
      const filterArgs: { query: string; lang?: string; cursor?: string | null; limit?: number } = { query };
      if (lang !== undefined) filterArgs.lang = lang;
      if (cursor !== undefined) filterArgs.cursor = cursor;
      if (limit !== undefined) filterArgs.limit = limit;

      const tasks = await todoist.getTasksByFilter(filterArgs);
      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify(tasks, null, 2),
          },
        ],
      };
    } catch (error) {
      return {
        content: [
          {
            type: 'text',
            text: `Error fetching tasks by filter "${query}": ${error instanceof Error ? error.message : String(error)}`,
          },
        ],
        isError: true,
      };
    }
  }
);

server.tool(
  'complete_task',
  {
    id: z.string().describe('The ID of the task to complete'),
  },
  async ({ id }) => {
    try {
      await todoist.closeTask(id);
      return {
        content: [
          {
            type: 'text',
            text: `Task with ID ${id} completed successfully`,
          },
        ],
      };
    } catch (error) {
      return {
        content: [
          {
            type: 'text',
            text: `Error completing task: ${error instanceof Error ? error.message : String(error)}`,
          },
        ],
        isError: true,
      };
    }
  }
);

// Note: Filter management tools (list_filters, create_filter, delete_filter) are not implemented
// because they are only available through the Todoist Sync API, not the REST API that the
// @doist/todoist-api-typescript client wraps. Filter operations would require implementing
// direct Sync API calls, which is beyond the scope of this REST API-based MCP server.

// Start the server with stdio transport
const transport = new StdioServerTransport();
await server.connect(transport);
