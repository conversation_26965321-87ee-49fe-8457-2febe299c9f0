version: '3'

tasks:
  compile:
    cmds:
      - yarn tsc -p tsconfig.build.json

  lint:
    cmds:
      - yarn eslint .

  lint-fix:
    cmds:
      - yarn eslint . --fix

  format:
    cmds:
      - yarn prettier . --write

  format-check:
    cmds:
      - yarn prettier . --check

  build:
    - task: format-check
    - task: lint
    - task: compile

  dev:
    desc: Run the MCP server in development mode
    cmds:
      - yarn tsx src/index.ts

  inspect:
    desc: Run the MCP inspector to test the server
    cmds:
      - task: compile
      - cmd: |
          source .env
          yarn dlx @modelcontextprotocol/inspector node dist/index.js
