# home-mcp

A Model Context Protocol (MCP) server that provides Todoist integration tools for AI assistants and other MCP clients.

## Overview

This MCP server exposes a comprehensive set of tools for interacting with Todoist, allowing you to manage projects, tasks, and labels programmatically. It's built using the official Todoist API TypeScript client and follows MCP standards for seamless integration with AI assistants.

## Features

- **Project Management**: List all your Todoist projects
- **Section Management**: Create, list, update, and delete sections within projects
- **Task Management**: Create, read, update, delete, and complete tasks
- **Label Management**: Create, list, and delete labels
- **Advanced Filtering**: Query tasks using Todoist's powerful filter syntax
- **Task Organization**: Move tasks between sections and projects
- **Error Handling**: Comprehensive error handling with detailed error messages

## Prerequisites

- Node.js 22 or higher
- Yarn package manager
- A Todoist account and API token

## Setup

### 1. Get Your Todoist API Token

1. Go to [Todoist Settings > Integrations](https://todoist.com/prefs/integrations)
2. Scroll down to the "API token" section
3. Copy your API token (keep this secure!)

### 2. Install Dependencies

```bash
yarn install
```

### 3. Build the Project

```bash
yarn build
```

## Usage

### Running the Server

You can provide your Todoist API token in two ways:

#### Option 1: Environment Variable

```bash
export TODOIST_API_TOKEN="your_api_token_here"
node dist/index.js
```

#### Option 2: Command Line Argument

```bash
node dist/index.js --todoist-token "your_api_token_here"
```

The server will start and communicate via stdio, ready to receive MCP requests.

## Available Tools

### 1. `ping`

A simple test tool to verify the server is working.

**Parameters:** None

**Returns:** "pong"

**Example Usage:**

```json
{
  "method": "tools/call",
  "params": {
    "name": "ping"
  }
}
```

### 2. `list_projects`

Lists all your Todoist projects.

**Parameters:** None

**Returns:** JSON array of project objects with details like ID, name, color, etc.

**Example Usage:**

```json
{
  "method": "tools/call",
  "params": {
    "name": "list_projects"
  }
}
```

### 3. `list_tasks`

Lists all tasks from a specific project.

**Parameters:**

- `projectId` (string, required): The ID of the project to list tasks from

**Returns:** JSON array of task objects

**Example Usage:**

```json
{
  "method": "tools/call",
  "params": {
    "name": "list_tasks",
    "arguments": {
      "projectId": "2203306141"
    }
  }
}
```

### 4. `create_task`

Creates a new task in Todoist.

**Parameters:**

- `content` (string, required): The content/title of the task
- `projectId` (string, required): The ID of the project to create the task in
- `description` (string, optional): Optional description for the task
- `dueString` (string, optional): Optional due date in natural language (e.g., "tomorrow", "next Monday")
- `priority` (number, optional): Task priority (1-4, where 4 is highest priority)
- `sectionId` (string, optional): Optional ID of the section to create the task in

**Returns:** JSON object of the created task

**Example Usage:**

```json
{
  "method": "tools/call",
  "params": {
    "name": "create_task",
    "arguments": {
      "content": "Review quarterly reports",
      "projectId": "2203306141",
      "description": "Review Q4 financial reports and prepare summary",
      "dueString": "tomorrow",
      "priority": 3
    }
  }
}
```

### 5. `update_task`

Updates an existing task.

**Parameters:**

- `id` (string, required): The ID of the task to update
- `content` (string, optional): The new content/title of the task
- `description` (string, optional): The new description for the task
- `dueString` (string, optional): The new due date in natural language
- `priority` (number, optional): The new task priority (1-4, where 4 is highest priority)
- `labels` (array of strings, optional): Array of label names to assign to the task

**Returns:** JSON object of the updated task

**Example Usage:**

```json
{
  "method": "tools/call",
  "params": {
    "name": "update_task",
    "arguments": {
      "id": "7498765432",
      "content": "Review quarterly reports - URGENT",
      "priority": 4,
      "labels": ["work", "urgent"]
    }
  }
}
```

### 6. `delete_task`

Deletes a task from Todoist.

**Parameters:**

- `id` (string, required): The ID of the task to delete

**Returns:** Success confirmation message

**Example Usage:**

```json
{
  "method": "tools/call",
  "params": {
    "name": "delete_task",
    "arguments": {
      "id": "7498765432"
    }
  }
}
```

### 7. `complete_task`

Marks a task as completed.

**Parameters:**

- `id` (string, required): The ID of the task to complete

**Returns:** Success confirmation message

**Example Usage:**

```json
{
  "method": "tools/call",
  "params": {
    "name": "complete_task",
    "arguments": {
      "id": "7498765432"
    }
  }
}
```

### 8. `list_labels`

Lists all your Todoist labels.

**Parameters:** None

**Returns:** JSON array of label objects with details like ID, name, color, etc.

**Example Usage:**

```json
{
  "method": "tools/call",
  "params": {
    "name": "list_labels"
  }
}
```

### 9. `create_label`

Creates a new label in Todoist.

**Parameters:**

- `name` (string, required): The name of the label
- `color` (string, optional): The color of the label (e.g., "berry_red", "red", "orange", "yellow", "olive_green", "lime_green", "green", "mint_green", "teal", "sky_blue", "light_blue", "blue", "grape", "violet", "lavender", "magenta", "salmon", "charcoal", "grey", "taupe")
- `order` (number, optional): The order of the label
- `isFavorite` (boolean, optional): Whether the label is a favorite

**Returns:** JSON object of the created label

**Example Usage:**

```json
{
  "method": "tools/call",
  "params": {
    "name": "create_label",
    "arguments": {
      "name": "urgent",
      "color": "red",
      "isFavorite": true
    }
  }
}
```

### 10. `delete_label`

Deletes a label from Todoist.

**Parameters:**

- `id` (string, required): The ID of the label to delete

**Returns:** Success confirmation message

**Example Usage:**

```json
{
  "method": "tools/call",
  "params": {
    "name": "delete_label",
    "arguments": {
      "id": "2156154810"
    }
  }
}
```

### 11. `get_tasks_by_filter`

Retrieves tasks using Todoist's powerful filter syntax.

**Parameters:**

- `query` (string, required): The filter query string (e.g., "today", "overdue", "@work", "p1")
- `lang` (string, optional): Language for natural language processing (e.g., "en", "es", "fr")
- `cursor` (string, optional): Cursor for pagination
- `limit` (number, optional): Maximum number of tasks to return (1-200, default: 50)

**Returns:** JSON array of tasks matching the filter

**Example Usage:**

```json
{
  "method": "tools/call",
  "params": {
    "name": "get_tasks_by_filter",
    "arguments": {
      "query": "today & @work",
      "limit": 20
    }
  }
}
```

**Common Filter Examples:**

- `"today"` - Tasks due today
- `"overdue"` - Overdue tasks
- `"p1"` - Priority 1 tasks
- `"@work"` - Tasks with the "work" label
- `"today & @work"` - Tasks due today with the "work" label
- `"7 days"` - Tasks due in the next 7 days

### 12. `list_sections`

Lists all sections within a specific project.

**Parameters:**

- `projectId` (string, required): The ID of the project to list sections from

**Returns:** JSON array of section objects

**Example Usage:**

```json
{
  "method": "tools/call",
  "params": {
    "name": "list_sections",
    "arguments": {
      "projectId": "2203306141"
    }
  }
}
```

### 13. `create_section`

Creates a new section within a project.

**Parameters:**

- `name` (string, required): The name of the section
- `projectId` (string, required): The ID of the project to create the section in
- `order` (number, optional): Section position among other sections from the same project

**Returns:** JSON object of the created section

**Example Usage:**

```json
{
  "method": "tools/call",
  "params": {
    "name": "create_section",
    "arguments": {
      "name": "In Progress",
      "projectId": "2203306141",
      "order": 1
    }
  }
}
```

### 14. `update_section`

Updates an existing section.

**Parameters:**

- `id` (string, required): The ID of the section to update
- `name` (string, required): The new name of the section

**Returns:** JSON object of the updated section

**Example Usage:**

```json
{
  "method": "tools/call",
  "params": {
    "name": "update_section",
    "arguments": {
      "id": "7025",
      "name": "Completed Tasks"
    }
  }
}
```

### 15. `delete_section`

Deletes a section from a project.

**Parameters:**

- `id` (string, required): The ID of the section to delete

**Returns:** Success confirmation message

**Example Usage:**

```json
{
  "method": "tools/call",
  "params": {
    "name": "delete_section",
    "arguments": {
      "id": "7025"
    }
  }
}
```

### 16. `move_task_to_section`

Moves a task to a different section.

**Parameters:**

- `taskId` (string, required): The ID of the task to move
- `sectionId` (string, required): The ID of the section to move the task to

**Returns:** JSON object of the updated task

**Example Usage:**

```json
{
  "method": "tools/call",
  "params": {
    "name": "move_task_to_section",
    "arguments": {
      "taskId": "2995104339",
      "sectionId": "7025"
    }
  }
}
```

## Limitations

- **Filter Management**: Filter management tools (list_filters, create_filter, delete_filter) are not implemented because they are only available through the Todoist Sync API, not the REST API that this server uses.

## Development

### Building

```bash
yarn build
```

### Linting

```bash
yarn lint:fix
```

### Formatting

```bash
yarn format
```

## Dependencies

- **@modelcontextprotocol/sdk**: MCP SDK for building MCP servers
- **@doist/todoist-api-typescript**: Official Todoist API TypeScript client
- **commander**: Command-line argument parsing
- **zod**: Schema validation for tool parameters

## License

This project is part of a personal home automation MCP server collection.

## Contributing

This is a personal project, but feel free to fork and adapt for your own use cases.
